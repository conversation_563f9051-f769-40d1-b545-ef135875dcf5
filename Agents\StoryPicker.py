from Helpers.LLMRequest import create_llm_client, ModelType

def StoryPickerAgent(used_topics: list[str], topics: list[str]) -> list[str]:
    llm = create_llm_client(
        system_instruction=f"""# Youtube Video Content Selector

## Task Information
Your job is to create 3 ideas based on the provided tends and news information.
You should select Finance related topics for the YouTube channel Liquid Finance.

## Don't repeat topics
You will be provided with a list of storys that have already been used today, we dont want to reuse topics.
Example if we already did a story on bitcoin, dont do another this applies to all topics.
We want to keep content unique and engaging.

## Examples of Good Topics
- Bitcoin Market Today
- YMAX Stock Today
- TSLA Stock Today
- Top Market Movers Today

## Rules
- Avoid things like "Best Stocks to Buy" or "Worst Stocks to Buy"
- Keep information impartive and to the point
- We provide information not financial advice
- Do not provide false information
- Do not provide misleading information
- Keep all topics related to finance
- Avoid politics
- Dont overhype stocks/crypto/commodities
- Up to 3 topic ideas per response
- Dont reuse topics if we did a story on bitcoin today, dont do another one (same with all other topics)
- Use the provided trending search terms and news headlines to create perfect topics

## Storys Used Today
{used_topics}

## Trending Search Terms
{topics}

## Output Format
You should follow this exact custom XML format:
<topics><topic>Topic 1</topic><topic>Topic 2</topic><topic>Topic 3</topic></topics>
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH_LIGHT
    )

    response = llm.generate_response(f"""## Storys Used Today
{used_topics}

## Trending Topics:
{topics}
""")
    if response is None:
        return []

    # Parse response
    split_one = response.split("<topics>")[1]
    split_two = split_one.split("</topics>")[0]
    split_three = split_two.split("<topic>")[1:]
    topics = [split_three[i].split("</topic>")[0] for i in range(len(split_three))]

    return topics
