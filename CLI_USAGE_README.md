# YTFinance CLI Usage Guide

## Overview

The YTFinance workflow now includes comprehensive command-line interface (CLI) functionality with video tracking, statistics, and scheduling capabilities. This system tracks all created videos, prevents duplicate content, and provides detailed analytics.

## Features

### 🎯 **Command-Line Interface**
- **`--run`**: Create and upload new videos
- **`--reset`**: Reset video history (for scheduled cleanup)
- **`--stats`**: Display comprehensive statistics
- **`--list`**: Show recent videos with details
- **`--upload`**: Upload current video in temp directory
- **`--no-upload`**: Create videos without uploading

### 📊 **Video Tracking System**
- **Persistent Storage**: All videos tracked in `video_history.json`
- **Duplicate Detection**: Prevents similar topics/titles
- **Upload Status**: Tracks pending, uploaded, and failed videos
- **Analytics**: Daily, weekly, monthly statistics

### ⏰ **Scheduling Support**
- **Cron Integration**: Automated daily/weekly resets
- **Backup Creation**: Automatic backups before resets
- **Log Management**: Detailed logging for scheduled runs

## Command Reference

### Basic Commands

#### Create New Video
```bash
python workflow.py --run
```
- Creates and uploads a new video
- Checks for duplicate topics
- Tracks video in history
- Full pipeline execution

#### Show Statistics
```bash
python workflow.py --stats
```
- Total videos created
- Upload success rate
- Daily/weekly/monthly counts
- Status breakdown

#### List Recent Videos
```bash
python workflow.py --list
python workflow.py --list --days 30
```
- Shows recent videos (default: 7 days)
- Displays title, topic, status, URL
- Color-coded status indicators

#### Reset Video History
```bash
python workflow.py --reset
```
- Clears all video history
- Creates automatic backup
- Useful for scheduled cleanup

### Advanced Options

#### Create Without Upload
```bash
python workflow.py --run --no-upload
```
- Creates video content only
- Skips YouTube upload step
- Useful for testing or manual upload

#### Upload Current Video
```bash
python workflow.py --upload
```
- Uploads the current video in temp directory
- Uses existing metadata if available
- Perfect for uploading videos created with --no-upload
- Automatically finds video and metadata files

#### Custom Storage File
```bash
python workflow.py --stats --storage-file custom_history.json
```
- Use different history file
- Useful for multiple accounts or testing

#### Verbose Logging
```bash
python workflow.py --run --verbose
```
- Enable detailed debug logging
- Shows all API calls and responses
- Helpful for troubleshooting

#### Quiet Mode
```bash
python workflow.py --run --quiet
```
- Suppress all output except critical errors
- Clean minimal output for automation/scripting
- Perfect for cron jobs and background processes

## Video Tracking

### Storage Format

Videos are tracked in `video_history.json`:

```json
{
  "videos": [
    {
      "title": "Market Analysis: Tech Stocks Surge",
      "topic": "Technology Stock Market Analysis",
      "created_at": "2024-01-15T10:30:00",
      "video_id": "ABC123",
      "video_url": "https://youtube.com/watch?v=ABC123",
      "upload_status": "uploaded",
      "privacy": "unlisted",
      "tags": ["tech", "stocks", "market"],
      "description_length": 250,
      "video_duration": 45.5,
      "file_path": "temp/assembled_video.mp4"
    }
  ],
  "last_updated": "2024-01-15T10:30:00",
  "total_videos": 1
}
```

### Duplicate Detection

The system prevents duplicate content using similarity matching:

- **Topic Similarity**: 80% threshold for topics
- **Title Similarity**: 90% threshold for titles
- **Smart Matching**: Uses sequence matching algorithms
- **Automatic Skip**: Duplicate topics are automatically skipped

### Status Tracking

Videos have three possible statuses:

- **`pending`**: Video created but not uploaded
- **`uploaded`**: Successfully uploaded to YouTube
- **`failed`**: Upload failed after all retries

## Scheduling with Cron

### Daily Reset (Midnight)
```bash
# Add to crontab (crontab -e)
0 0 * * * cd /path/to/YTFinance && python cron_reset.py >> logs/cron.log 2>&1
```

### Weekly Reset (Sunday Midnight)
```bash
# Add to crontab (crontab -e)
0 0 * * 0 cd /path/to/YTFinance && python cron_reset.py >> logs/cron.log 2>&1
```

### Automated Video Creation
```bash
# Create video every 4 hours
0 */4 * * * cd /path/to/YTFinance && python workflow.py --run >> logs/workflow.log 2>&1
```

### Combined Schedule Example
```bash
# Reset at midnight, create videos every 4 hours
0 0 * * * cd /path/to/YTFinance && python cron_reset.py >> logs/cron.log 2>&1
0 */4 * * * cd /path/to/YTFinance && python workflow.py --run >> logs/workflow.log 2>&1
```

## Statistics Dashboard

### Sample Output

```
📊 Video Creation Statistics
==================================================

📈 Video Statistics
┌─────────────────────┬───────┐
│ Metric              │ Value │
├─────────────────────┼───────┤
│ Total Videos        │ 25    │
│ Uploaded Videos     │ 23    │
│ Pending Videos      │ 1     │
│ Failed Videos       │ 1     │
│ Today's Videos      │ 3     │
│ This Week           │ 15    │
│ This Month          │ 25    │
│ Upload Success Rate │ 95.8% │
└─────────────────────┴───────┘
```

### Recent Videos List

```
📋 Recent Videos (Last 7 days)
==================================================

📹 Recent Videos (15 found)
┌──────────┬─────────────────────────────────────────┬──────────────────────────────┬──────────┬─────────────────────────────────────────────────┐
│ Date     │ Title                                   │ Topic                        │ Status   │ URL                                             │
├──────────┼─────────────────────────────────────────┼──────────────────────────────┼──────────┼─────────────────────────────────────────────────┤
│ 01/15 10 │ Market EXPLODES! Tech Stocks Surge     │ Technology Stock Analysis    │ UPLOADED │ https://youtube.com/watch?v=ABC123              │
│ 01/15 14 │ Crypto Alert: Bitcoin Breaks $50K      │ Cryptocurrency Update        │ UPLOADED │ https://youtube.com/watch?v=DEF456              │
│ 01/15 18 │ Fed Decision Impact on Markets          │ Federal Reserve Analysis     │ PENDING  │ N/A                                             │
└──────────┴─────────────────────────────────────────┴──────────────────────────────┴──────────┴─────────────────────────────────────────────────┘
```

## Error Handling

### Common Issues

#### No New Topics Found
```
⚠️ No new story topics found, waiting 1 hour...
```
**Solution**: System automatically waits and retries. Topics may be exhausted for the day.

#### Duplicate Topic Detected
```
⚠️ Skipping duplicate topic: Technology Stock Market Analysis
```
**Solution**: System automatically skips and tries next topic. This prevents repetitive content.

#### Upload Failed
```
⚠️ YouTube upload failed after 3 attempts: Connection timeout
```
**Solution**: Video is marked as "pending" and can be manually uploaded later.

### Recovery Commands

#### Check Failed Uploads
```bash
python workflow.py --list | grep FAILED
```

#### Reset After Issues
```bash
python workflow.py --reset
```

#### Manual Upload Retry
```bash
python workflow.py --run --no-upload  # Create new content
python workflow.py --upload           # Upload the created video
```

## File Management

### Generated Files

```
YTFinance/
├── video_history.json              # Main video tracking
├── video_history_backup_*.json     # Automatic backups
├── logs/
│   ├── cron.log                    # Cron execution logs
│   ├── workflow.log                # Workflow execution logs
│   └── cron_reset.log              # Reset operation logs
└── temp/
    ├── assembled_video.mp4         # Latest video
    ├── youtube_metadata.json       # Video metadata
    └── youtube_upload_info.json    # Upload details
```

### Backup Management

- **Automatic Backups**: Created before each reset
- **Timestamped**: Format `video_history_backup_YYYYMMDD_HHMMSS.json`
- **Manual Export**: Use `VideoTracker.export_data()` method
- **Retention**: Manage backup files manually or via script

## Integration Examples

### GitHub Actions Workflow
```yaml
name: YTFinance Auto Upload
on:
  schedule:
    - cron: '0 */4 * * *'  # Every 4 hours
jobs:
  create-video:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Create video
        run: python workflow.py --run
```

### Docker Integration
```dockerfile
FROM python:3.9
WORKDIR /app
COPY . .
RUN pip install -r requirements.txt
CMD ["python", "workflow.py", "--run"]
```

### Monitoring Script
```bash
#!/bin/bash
# monitor_ytfinance.sh
cd /path/to/YTFinance
python workflow.py --stats | grep "Upload Success Rate" | mail -s "YTFinance Stats" <EMAIL>
```

## Best Practices

### 1. **Regular Monitoring**
- Check statistics daily: `python workflow.py --stats`
- Review recent videos: `python workflow.py --list`
- Monitor logs for errors

### 2. **Scheduled Maintenance**
- Reset history weekly/monthly to prevent topic exhaustion
- Clean up old backup files periodically
- Update trending topics and keywords

### 3. **Content Quality**
- Review uploaded videos periodically
- Adjust privacy settings as needed
- Monitor engagement metrics

### 4. **System Health**
- Test CLI commands regularly: `python test_cli_workflow.py`
- Verify YouTube login sessions
- Check disk space for video files

## Troubleshooting

### Reset Everything
```bash
# Complete reset
rm video_history.json
rm video_history_backup_*.json
python workflow.py --reset
```

### Debug Mode
```bash
# Enable verbose logging
python workflow.py --run --verbose
```

### Quiet Mode (for automation)
```bash
# Minimal output for scripts/cron
python workflow.py --run --quiet
```

### Test CLI
```bash
# Run comprehensive tests
python test_cli_workflow.py
```
