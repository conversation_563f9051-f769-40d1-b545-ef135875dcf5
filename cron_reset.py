#!/usr/bin/env python3
"""
Cron Reset Script for YTFinance

This script is designed to be run via cron at midnight to reset the video history.
It provides a clean slate for each day's video creation cycle.

Usage:
    python cron_reset.py

Cron Example (reset at midnight daily):
    0 0 * * * cd /path/to/YTFinance && python cron_reset.py >> logs/cron.log 2>&1

Cron Example (reset at midnight on Sundays for weekly cycle):
    0 0 * * 0 cd /path/to/YTFinance && python cron_reset.py >> logs/cron.log 2>&1
"""

import sys
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from Helpers.VideoTracker import VideoTracker

def setup_logging():
    """Set up logging for cron execution"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / "cron_reset.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Also log to console
        ]
    )

def main():
    """Main cron reset function"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 50)
    logger.info("YTFinance Cron Reset Started")
    logger.info("=" * 50)
    
    try:
        # Initialize video tracker
        video_tracker = VideoTracker("video_history.json")
        
        # Get current stats before reset
        stats = video_tracker.get_statistics()
        logger.info(f"Current video count: {stats['total_videos']}")
        
        if stats["total_videos"] == 0:
            logger.info("No video history to reset.")
            return
        
        # Create backup before reset
        backup_file = f"video_history_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        video_tracker.export_data(backup_file)
        logger.info(f"Backup created: {backup_file}")
        
        # Reset the video history
        video_tracker.reset_data()
        logger.info(f"Reset complete! Cleared {stats['total_videos']} video records.")
        
        # Log summary
        logger.info("Reset Summary:")
        logger.info(f"  - Videos cleared: {stats['total_videos']}")
        logger.info(f"  - Uploaded videos: {stats['uploaded_videos']}")
        logger.info(f"  - Pending videos: {stats['pending_videos']}")
        logger.info(f"  - Failed videos: {stats['failed_videos']}")
        logger.info(f"  - Backup file: {backup_file}")
        
        logger.info("YTFinance ready for new video creation cycle!")
        
    except Exception as e:
        logger.error(f"Error during cron reset: {e}")
        sys.exit(1)
    
    logger.info("YTFinance Cron Reset Completed Successfully")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
