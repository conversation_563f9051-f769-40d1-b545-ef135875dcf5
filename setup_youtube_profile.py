#!/usr/bin/env python3
"""
YouTube Profile Setup Script

This script helps you set up a persistent YouTube login profile
for automated video uploads. Run this once to save your login session.
"""

import sys
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from Helpers.YouTubeUploader import YouTubeUploader

def setup_youtube_profile():
    """Set up YouTube login profile for automated uploads"""
    print("🚀 YouTube Profile Setup")
    print("=" * 50)
    print()
    print("This script will help you set up a persistent login profile")
    print("for automated YouTube uploads. You only need to do this once.")
    print()
    
    # Get profile name
    profile_name = input("Enter profile name (default: ytfinance_bot): ").strip()
    if not profile_name:
        profile_name = "ytfinance_bot"
    
    print(f"\n📁 Creating profile: {profile_name}")
    print("🌐 Opening browser for YouTube login...")
    print()
    
    # Create uploader
    uploader = YouTubeUploader(
        profile_name=profile_name,
        headless=False,  # Keep visible for login
        timeout=60
    )
    
    try:
        # Attempt login
        print("🔐 Checking YouTube login status...")
        logged_in = uploader.ensure_logged_in()
        
        if logged_in:
            print("✅ Successfully logged in to YouTube!")
            print(f"💾 Login session saved in profile: {profile_name}")
            print()
            print("🎉 Setup complete! Your profile is ready for automated uploads.")
            print()
            print("Next steps:")
            print("1. Run the main workflow: python workflow.py")
            print("2. Videos will be automatically uploaded to YouTube")
            print("3. Check temp/youtube_upload_info.json for upload details")
            print()
            return True
        else:
            print("❌ Failed to log in to YouTube")
            print()
            print("Troubleshooting:")
            print("- Make sure you completed the login process in the browser")
            print("- Ensure you have a valid YouTube account")
            print("- Check your internet connection")
            print("- Try running the script again")
            return False
            
    except Exception as e:
        print(f"❌ Error during setup: {e}")
        return False
    finally:
        uploader.close()

def test_existing_profile():
    """Test if an existing profile works"""
    print("\n🧪 Testing Existing Profiles")
    print("-" * 30)
    
    profiles_dir = Path("browser_profiles")
    if not profiles_dir.exists():
        print("No existing profiles found.")
        return
    
    # List existing profiles
    profiles = [p.name for p in profiles_dir.iterdir() if p.is_dir()]
    if not profiles:
        print("No existing profiles found.")
        return
    
    print("Found existing profiles:")
    for i, profile in enumerate(profiles, 1):
        print(f"  {i}. {profile}")
    
    try:
        choice = input(f"\nSelect profile to test (1-{len(profiles)}, or Enter to skip): ").strip()
        if not choice:
            return
        
        profile_index = int(choice) - 1
        if 0 <= profile_index < len(profiles):
            profile_name = profiles[profile_index]
            
            print(f"\n🔍 Testing profile: {profile_name}")
            
            uploader = YouTubeUploader(
                profile_name=profile_name,
                headless=False,
                timeout=30
            )
            
            try:
                logged_in = uploader.ensure_logged_in()
                if logged_in:
                    print(f"✅ Profile {profile_name} is working!")
                else:
                    print(f"❌ Profile {profile_name} needs re-authentication")
            finally:
                uploader.close()
        else:
            print("Invalid selection.")
            
    except ValueError:
        print("Invalid input.")
    except Exception as e:
        print(f"Error testing profile: {e}")

def main():
    """Main setup function"""
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    print("Welcome to YouTube Upload Setup!")
    print()
    
    # Check for existing profiles first
    test_existing_profile()
    
    # Ask if user wants to create new profile
    print("\n" + "=" * 50)
    create_new = input("Create new YouTube profile? (y/N): ").strip().lower()
    
    if create_new in ['y', 'yes']:
        success = setup_youtube_profile()
        if success:
            print("\n🎯 Ready to upload! Run 'python workflow.py' to start creating and uploading videos.")
        else:
            print("\n🔧 Setup incomplete. Please try again or check the troubleshooting guide.")
        return success
    else:
        print("\n👋 Setup skipped. Use existing profiles or run this script again to create new ones.")
        return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
