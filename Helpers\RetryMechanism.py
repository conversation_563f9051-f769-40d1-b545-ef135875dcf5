#!/usr/bin/env python3
"""
Retry mechanism with exponential backoff for API calls and LLM operations.

This module provides decorators and utility functions to handle retries
with exponential backoff for various operations like image generation,
LLM calls, and other API operations that may fail temporarily.
"""

import time
import random
import logging
import functools
from typing import Callable, Any, Optional, Tuple, Type, Union, List
from dataclasses import dataclass


@dataclass
class RetryConfig:
    """Configuration for retry mechanism"""
    max_retries: int = 10
    base_delay: float = 1.0  # Base delay in seconds
    max_delay: float = 60.0  # Maximum delay in seconds
    exponential_base: float = 2.0  # Exponential backoff multiplier
    jitter: bool = True  # Add random jitter to prevent thundering herd
    backoff_factor: float = 1.0  # Additional backoff factor


class RetryError(Exception):
    """Exception raised when all retry attempts are exhausted"""
    def __init__(self, message: str, last_exception: Exception, attempt_count: int):
        super().__init__(message)
        self.last_exception = last_exception
        self.attempt_count = attempt_count


class RetryMechanism:
    """
    Retry mechanism with exponential backoff and jitter.
    
    Features:
    - Exponential backoff with configurable base and maximum delays
    - Optional jitter to prevent thundering herd problems
    - Configurable retry count and exception types
    - Detailed logging of retry attempts
    - Support for both sync and async operations
    """
    
    def __init__(self, config: Optional[RetryConfig] = None):
        """
        Initialize retry mechanism
        
        Args:
            config: Retry configuration (uses defaults if None)
        """
        self.config = config or RetryConfig()
        self.logger = logging.getLogger(__name__)
    
    def calculate_delay(self, attempt: int) -> float:
        """
        Calculate delay for the given attempt number
        
        Args:
            attempt: Current attempt number (0-based)
            
        Returns:
            Delay in seconds
        """
        # Calculate exponential backoff
        delay = self.config.base_delay * (self.config.exponential_base ** attempt)
        delay *= self.config.backoff_factor
        
        # Apply maximum delay limit
        delay = min(delay, self.config.max_delay)
        
        # Add jitter if enabled
        if self.config.jitter:
            # Add random jitter of ±25% of the delay
            jitter_range = delay * 0.25
            jitter = random.uniform(-jitter_range, jitter_range)
            delay += jitter
        
        # Ensure delay is not negative
        return max(0.1, delay)
    
    def retry_operation(
        self,
        operation: Callable,
        *args,
        exception_types: Tuple[Type[Exception], ...] = (Exception,),
        operation_name: str = "operation",
        **kwargs
    ) -> Any:
        """
        Execute an operation with retry logic
        
        Args:
            operation: Function to execute
            *args: Arguments to pass to the operation
            exception_types: Tuple of exception types to retry on
            operation_name: Name of the operation for logging
            **kwargs: Keyword arguments to pass to the operation
            
        Returns:
            Result of the successful operation
            
        Raises:
            RetryError: If all retry attempts are exhausted
        """
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):  # +1 for initial attempt
            try:
                if attempt > 0:
                    delay = self.calculate_delay(attempt - 1)
                    self.logger.info(
                        f"Retrying {operation_name} (attempt {attempt + 1}/{self.config.max_retries + 1}) "
                        f"after {delay:.2f}s delay..."
                    )
                    time.sleep(delay)
                else:
                    self.logger.debug(f"Executing {operation_name} (initial attempt)")
                
                # Execute the operation
                result = operation(*args, **kwargs)
                
                if attempt > 0:
                    self.logger.info(f"✅ {operation_name} succeeded on attempt {attempt + 1}")
                
                return result
                
            except exception_types as e:
                last_exception = e
                
                if attempt < self.config.max_retries:
                    self.logger.warning(
                        f"❌ {operation_name} failed on attempt {attempt + 1}: {str(e)}"
                    )
                else:
                    self.logger.error(
                        f"💥 {operation_name} failed after {self.config.max_retries + 1} attempts: {str(e)}"
                    )
                    break
            
            except Exception as e:
                # Don't retry on unexpected exceptions
                self.logger.error(f"💥 {operation_name} failed with unexpected error: {str(e)}")
                raise e
        
        # All retries exhausted
        raise RetryError(
            f"Operation '{operation_name}' failed after {self.config.max_retries + 1} attempts",
            last_exception,
            self.config.max_retries + 1
        )


def with_retry(
    max_retries: int = 10,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    jitter: bool = True,
    exception_types: Tuple[Type[Exception], ...] = (Exception,),
    operation_name: Optional[str] = None
):
    """
    Decorator to add retry logic to any function
    
    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        exponential_base: Exponential backoff multiplier
        jitter: Whether to add random jitter
        exception_types: Tuple of exception types to retry on
        operation_name: Name for logging (uses function name if None)
        
    Returns:
        Decorated function with retry logic
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            config = RetryConfig(
                max_retries=max_retries,
                base_delay=base_delay,
                max_delay=max_delay,
                exponential_base=exponential_base,
                jitter=jitter
            )
            
            retry_mechanism = RetryMechanism(config)
            name = operation_name or func.__name__
            
            return retry_mechanism.retry_operation(
                func,
                *args,
                exception_types=exception_types,
                operation_name=name,
                **kwargs
            )
        
        return wrapper
    return decorator


# Convenience functions for common retry scenarios
def create_image_retry_mechanism() -> RetryMechanism:
    """Create retry mechanism optimized for image generation"""
    config = RetryConfig(
        max_retries=10,
        base_delay=2.0,
        max_delay=120.0,
        exponential_base=1.5,
        jitter=True
    )
    return RetryMechanism(config)


def create_llm_retry_mechanism() -> RetryMechanism:
    """Create retry mechanism optimized for LLM calls"""
    config = RetryConfig(
        max_retries=10,
        base_delay=1.0,
        max_delay=60.0,
        exponential_base=2.0,
        jitter=True
    )
    return RetryMechanism(config)


def create_api_retry_mechanism() -> RetryMechanism:
    """Create retry mechanism optimized for general API calls"""
    config = RetryConfig(
        max_retries=5,
        base_delay=1.0,
        max_delay=30.0,
        exponential_base=2.0,
        jitter=True
    )
    return RetryMechanism(config)


# Example usage
if __name__ == "__main__":
    import logging
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Example 1: Using decorator
    @with_retry(max_retries=3, base_delay=0.5, exception_types=(ValueError,))
    def flaky_function(x):
        """Function that fails randomly"""
        import random
        if random.random() < 0.7:  # 70% chance of failure
            raise ValueError("Random failure!")
        return f"Success with {x}"
    
    # Example 2: Using retry mechanism directly
    def another_flaky_function(y):
        import random
        if random.random() < 0.8:  # 80% chance of failure
            raise ConnectionError("Network error!")
        return f"Connected with {y}"
    
    try:
        # Test decorator
        result1 = flaky_function("test1")
        print(f"Decorator result: {result1}")
        
        # Test direct usage
        retry_mechanism = create_api_retry_mechanism()
        result2 = retry_mechanism.retry_operation(
            another_flaky_function,
            "test2",
            exception_types=(ConnectionError,),
            operation_name="network_operation"
        )
        print(f"Direct usage result: {result2}")
        
    except RetryError as e:
        print(f"All retries failed: {e}")
        print(f"Last exception: {e.last_exception}")
        print(f"Attempts made: {e.attempt_count}")
